// kafka.service.ts
import { Injectable } from '@nestjs/common';
import { Kafka, Producer } from 'kafkajs';

@Injectable()
export class KafkaService {
  private kafka: Kafka;
  private producer: Producer;

  constructor() {
    this.kafka = new Kafka({
      clientId: 'coddn-api-' + process.env.ENV,
      brokers: JSON.parse(process.env.MSK_BROKERS),
      ssl: true,
      sasl: { 
        mechanism: 'aws',
        authorizationIdentity: process.env.AWS_USER_ID,
        accessKeyId: process.env.AWS_ACCESS_KEY_ID,
        secretAccessKey: process.env.AWS_SERCRET_KEY_ID,
      },
    });
    this.producer = this.kafka.producer();
  }

  async onModuleInit() {
    await this.producer.connect();
  }

  async sendMessage(topic: string, message: any) {
    try {
      await this.producer.send({
        topic: topic,
        messages: [{ value: JSON.stringify(message) }],
      });
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  }

  async onModuleDestroy() {
    await this.producer.disconnect();
  }
}