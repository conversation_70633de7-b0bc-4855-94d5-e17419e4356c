// kafka.controller.ts
import { Controller, Post, Body, Param } from '@nestjs/common';
import { KafkaService } from './kafka.service';

@Controller('kafka')
export class KafkaController {
  constructor(private readonly kafkaService: KafkaService) {}

  @Post('send/:topic')
  async sendMessage(@Param('topic') topic: string, @Body() message: any) {
    await this.kafkaService.sendMessage(topic, message);
    return { message: 'Message sent' };
  }
}